<?php $__env->startSection('page_title', 'Gestion des Paiements ADRA & TEAM 3'); ?>

<?php $__env->startSection('page_style'); ?>
<style>
/* Modern DataTable Styling */
.table-modern {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table-modern thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 15px 12px;
    border: none;
    position: relative;
}

.table-modern thead th:first-child {
    border-top-left-radius: 8px;
}

.table-modern thead th:last-child {
    border-top-right-radius: 8px;
}

.table-modern tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #e9ecef;
}

.table-modern tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-modern tbody td {
    padding: 12px;
    vertical-align: middle;
    border: none;
}

.table-modern tbody tr:last-child td:first-child {
    border-bottom-left-radius: 8px;
}

.table-modern tbody tr:last-child td:last-child {
    border-bottom-right-radius: 8px;
}

/* Badge Styling */
.badge-modern {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Status Badges */
.status-adra {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.status-team3 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

/* Form Controls */
.form-control-sm {
    border-radius: 6px;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.form-control-sm:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Payment Selection */
.payment-selection {
    max-width: 200px;
}

.form-check-inline {
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

/* Action Buttons */
.btn-modern {
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes  spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .table-responsive {
        border-radius: 8px;
    }

    .payment-selection {
        max-width: 150px;
    }

    .form-check-inline {
        display: block;
        margin-right: 0;
    }
}

/* DataTable Custom Styling */
.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border-radius: 6px;
    border: 1px solid #ddd;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: 6px;
    margin: 0 2px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

/* Summary Cards */
.border-left-success {
    border-left: 4px solid #28a745 !important;
}

.card.bg-primary, .card.bg-success, .card.bg-info, .card.bg-warning {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card.bg-primary .card-body,
.card.bg-success .card-body,
.card.bg-info .card-body,
.card.bg-warning .card-body {
    padding: 1rem;
}

/* Filter Section */
.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    min-height: 38px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 36px;
    padding-left: 12px;
}

/* Action Buttons */
.btn-group-vertical .btn {
    margin-bottom: 0;
}

.btn-group-vertical .btn:not(:last-child) {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-group-vertical .btn:not(:first-child) {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

/* Summary Display */
.font-weight-bold {
    font-weight: 600 !important;
}

.text-success {
    color: #28a745 !important;
}

.text-info {
    color: #17a2b8 !important;
}

.text-warning {
    color: #ffc107 !important;
}

/* Disabled state */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Hidden rows */
tr[style*="display: none"] {
    display: none !important;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<div class="card">
    <div class="card-header header-elements-inline">
        <h6 class="card-title">
            <i class="icon-credit-card mr-2"></i>
            Gestion des Paiements ADRA & TEAM 3
        </h6>
        <div class="header-elements">
            <div class="list-icons">
                <a class="list-icons-item" data-action="collapse"></a>
                <a class="list-icons-item" data-action="reload"></a>
                <a class="list-icons-item" data-action="remove"></a>
            </div>
        </div>
    </div>

    <div class="card-body">
        <!-- Filter Zone -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="class_selector">Choisir une classe :</label>
                    <select id="class_selector" class="form-control select2">
                        <option value="">-- Sélectionner une classe --</option>
                        <?php $__currentLoopData = $classes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $class): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($class->id); ?>" <?php echo e($class->id == $selectedClassId ? 'selected' : ''); ?>>
                                <?php echo e($class->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="payment_selector">Paiements créés dans cette classe :</label>
                    <select id="payment_selector" class="form-control select2" disabled>
                        <option value="">-- Choisir d'abord une classe --</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Test Section -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="alert alert-warning">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <strong>🔧 Mode Test :</strong> Vérifiez que les fonctions JavaScript fonctionnent
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-warning" onclick="testJavaScriptFunctions()">
                                <i class="icon-cog mr-1"></i>Tester JS
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Info -->
        <div class="row mb-3" id="summary-section" style="display: none;">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <div class="d-flex align-items-center">
                        <i class="icon-info22 mr-3" style="font-size: 1.5rem;"></i>
                        <div>
                            <strong>Classe sélectionnée :</strong> <span id="selected-class-name">-</span> |
                            <strong>Paiement :</strong> <span id="selected-payment-name">-</span> |
                            <strong>Montant :</strong> <span id="selected-payment-amount">0 Ar</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mb-3" id="action-section" style="display: none;">
            <div class="col-md-12 text-center">
                <button type="button" class="btn btn-success btn-lg" onclick="printAllReceipts()" id="print-all-btn">
                    <i class="icon-printer mr-2"></i>Imprimer tous les reçus thermiques 58mm
                </button>
            </div>
        </div>

        <!-- Students Table -->
        <div class="table-responsive" id="students-table" style="display: none;">
            <table id="adra_team3_table" class="table table-modern">
                <thead>
                    <tr>
                        <th>Nom & Prénoms</th>
                        <th>Classe</th>
                        <th>Statut</th>
                        <th>Code Référence</th>
                        <th>Montant Total du Paiement</th>
                        <th>Montant à Payer</th>
                        <th>Imprimer Reçu</th>
                    </tr>
                </thead>
                <tbody id="students-tbody">
                    <!-- Les étudiants seront chargés dynamiquement -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Chargement...</span>
                </div>
                <p class="mt-2">Traitement en cours...</p>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_script'); ?>
<script>
// Attendre que tout soit chargé
window.addEventListener('load', function() {
    console.log('🚀 Page complètement chargée, initialisation ADRA TEAM3...');

    // Vérification des dépendances
    if (typeof jQuery === 'undefined') {
        console.error('❌ jQuery is not loaded!');
        alert('Erreur: jQuery n\'est pas chargé. Veuillez actualiser la page.');
        return;
    } else {
        console.log('✅ jQuery is loaded');
    }

    // Initialiser avec jQuery
    $(document).ready(function() {
        console.log('ADRA TEAM3 Script loaded'); // Debug

        // Vérifier que tous les éléments existent
        if ($('#class_selector').length === 0) {
            console.error('❌ #class_selector not found!');
        } else {
            console.log('✅ #class_selector found');
        }

        if ($('#payment_selector').length === 0) {
            console.error('❌ #payment_selector not found!');
        } else {
            console.log('✅ #payment_selector found');
        }

    // Initialize Select2
    $('.select2').select2({
        placeholder: 'Sélectionner...',
        allowClear: true
    });

    // Event handler for class selection
    $('#class_selector').on('change', function() {
        console.log('Class selector changed:', $(this).val()); // Debug
        loadClassPayments();
    });

    // Event handler for payment selection
    $('#payment_selector').on('change', function() {
        console.log('Payment selector changed:', $(this).val()); // Debug
        loadStudentsWithPayment();
    });

    // Load class payments if class is already selected
    if ($('#class_selector').val()) {
        console.log('Auto-loading payments for pre-selected class'); // Debug
        loadClassPayments();
    }

        // Test function availability
        if (typeof loadClassPayments === 'function') {
            console.log('✅ loadClassPayments function is available');
        } else {
            console.error('❌ loadClassPayments function is NOT available');
        }
    });
});

// Fonctions globales définies en dehors de document.ready

// Load payments for selected class
function loadClassPayments() {
    const classId = $('#class_selector').val();

    if (!classId) {
        $('#payment_selector').prop('disabled', true).html('<option value="">-- Choisir d\'abord une classe --</option>');
        $('#summary-section, #action-section, #students-table').hide();
        return;
    }

    // Show loading
    $('#payment_selector').prop('disabled', true).html('<option value="">Chargement des paiements...</option>');

    console.log('Loading payments for class:', classId); // Debug

    // AJAX call to get payments for this class
    $.ajax({
        url: '<?php echo e(route("payments.adra_team3.get_payments")); ?>',
        method: 'GET',
        data: {
            class_id: classId
        },
        dataType: 'json',
        success: function(response) {
            console.log('Payments response:', response); // Debug

            let options = '<option value="">-- Sélectionner un paiement --</option>';

            if (response.success && response.payments && response.payments.length > 0) {
                response.payments.forEach(function(payment) {
                    const formattedAmount = new Intl.NumberFormat('fr-FR').format(payment.amount) + ' Ar';
                    options += `<option value="${payment.id}" data-amount="${payment.amount}">${payment.title} (${formattedAmount})</option>`;
                });
                $('#payment_selector').prop('disabled', false).html(options);
                $('#selected-class-name').text($('#class_selector option:selected').text());
            } else {
                options = '<option value="">Aucun paiement trouvé pour cette classe</option>';
                $('#payment_selector').prop('disabled', true).html(options);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading payments:', xhr.responseText); // Debug
            $('#payment_selector').prop('disabled', false).html('<option value="">Erreur lors du chargement</option>');

            if (typeof toastr !== 'undefined') {
                toastr.error('Erreur lors du chargement des paiements');
            } else {
                alert('Erreur lors du chargement des paiements');
            }
        }
    });
}

// Load students with selected payment
function loadStudentsWithPayment() {
    const classId = $('#class_selector').val();
    const paymentId = $('#payment_selector').val();

    if (!classId || !paymentId) {
        $('#summary-section, #action-section, #students-table').hide();
        return;
    }

    const selectedPayment = $('#payment_selector option:selected');
    const paymentName = selectedPayment.text();
    const paymentAmount = parseInt(selectedPayment.data('amount'));

    // Update summary
    $('#selected-payment-name').text(paymentName);
    $('#selected-payment-amount').text(formatCurrency(paymentAmount));
    $('#summary-section').show();

    // AJAX call to get students
    $.ajax({
        url: '<?php echo e(route("payments.adra_team3.get_students")); ?>',
        method: 'GET',
        data: {
            class_id: classId,
            payment_id: paymentId
        },
        dataType: 'json',
        success: function(response) {
            console.log('Students response:', response); // Debug

            if (response.success && response.students && response.students.length > 0) {
                let tableRows = '';

                response.students.forEach(function(student) {
                    const status = student.status;
                    const statusIcon = status === 'ADRA' ? '🏛️' : '👥';
                    const statusClass = status === 'ADRA' ? 'badge-info' : 'badge-success';
                    const amountToPay = status === 'ADRA' ? (paymentAmount * 0.75) : paymentAmount;
                    const balance = status === 'ADRA' ? (paymentAmount * 0.25) : 0;

                    tableRows += `
                        <tr data-student-id="${student.id}" data-status="${status}" data-payment-amount="${paymentAmount}">
                            <td>
                                <div class="font-weight-semibold">${student.name}</div>
                                <small class="text-muted">${student.adm_no || ''}</small>
                            </td>
                            <td>
                                <span class="badge badge-primary">${student.class_name}</span>
                            </td>
                            <td>
                                <span class="badge badge-modern ${statusClass}">${statusIcon} ${status}</span>
                            </td>
                            <td>
                                <input type="text" class="form-control form-control-sm reference-code"
                                       value="${student.reference_code}"
                                       data-student-id="${student.id}"
                                       onchange="updateReferenceCode(this)">
                            </td>
                            <td>
                                <strong>${formatCurrency(paymentAmount)}</strong>
                            </td>
                            <td>
                                <strong class="text-success">${formatCurrency(amountToPay)}</strong>
                                ${status === 'ADRA' ? `<br><small class="text-muted">Cash: ${formatCurrency(balance)}</small>` : ''}
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary" onclick="printIndividualReceipt(${student.id})">
                                    <i class="icon-printer"></i> Imprimer 58mm
                                </button>
                            </td>
                        </tr>
                    `;
                });

                $('#students-tbody').html(tableRows);
                $('#students-table, #action-section').show();

                // Initialize DataTable if not already done
                if (!$.fn.DataTable.isDataTable('#adra_team3_table')) {
                    $('#adra_team3_table').DataTable({
                        responsive: true,
                        pageLength: 25,
                        language: {
                            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json'
                        },
                        columnDefs: [
                            { orderable: false, targets: [6] }
                        ]
                    });
                }
            } else {
                $('#students-tbody').html('<tr><td colspan="7" class="text-center">Aucun étudiant ADRA ou TEAM3 trouvé pour ce paiement</td></tr>');
                $('#students-table').show();
                $('#action-section').hide();
            }
        },
        error: function() {
            $('#students-tbody').html('<tr><td colspan="7" class="text-center text-danger">Erreur lors du chargement des étudiants</td></tr>');
            $('#students-table').show();
            $('#action-section').hide();
        }
    });
}

// Update reference code
function updateReferenceCode(input) {
    const studentId = $(input).data('student-id');
    const referenceCode = $(input).val();

    $.ajax({
        url: '<?php echo e(route("payments.adra_team3.update_reference")); ?>',
        method: 'POST',
        data: {
            student_id: studentId,
            reference_code: referenceCode,
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                toastr.success('Code de référence mis à jour');
            }
        },
        error: function() {
            toastr.error('Erreur lors de la mise à jour');
        }
    });
}

// Print individual receipt
function printIndividualReceipt(studentId) {
    const row = $(`tr[data-student-id="${studentId}"]`);
    const paymentAmount = row.data('payment-amount');
    const status = row.data('status');
    const referenceCode = row.find('.reference-code').val();
    const paymentId = $('#payment_selector').val();

    if (!paymentId) {
        toastr.warning('Aucun paiement sélectionné');
        return;
    }

    // Show loading
    $('#loadingModal').modal('show');

    $.ajax({
        url: `<?php echo e(route('payments.adra_team3.print_receipt', '')); ?>/${studentId}`,
        method: 'POST',
        data: {
            payment_id: paymentId,
            payment_amount: paymentAmount,
            status: status,
            reference_code: referenceCode,
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            $('#loadingModal').modal('hide');
            // Open receipt in new window for printing
            const printWindow = window.open('', '_blank');
            printWindow.document.write(response);
            printWindow.document.close();
            printWindow.print();
        },
        error: function() {
            $('#loadingModal').modal('hide');
            toastr.error('Erreur lors de l\'impression');
        }
    });
}

// Print all receipts
function printAllReceipts() {
    const classId = $('#class_selector').val();
    const paymentId = $('#payment_selector').val();

    if (!classId || !paymentId) {
        toastr.warning('Veuillez sélectionner une classe et un paiement');
        return;
    }

    const studentsData = [];
    $('#students-tbody tr').each(function() {
        const studentId = $(this).data('student-id');
        const paymentAmount = $(this).data('payment-amount');
        const status = $(this).data('status');
        const referenceCode = $(this).find('.reference-code').val();

        if (studentId) {
            studentsData.push({
                student_id: studentId,
                payment_amount: paymentAmount,
                status: status,
                reference_code: referenceCode
            });
        }
    });

    if (studentsData.length === 0) {
        toastr.warning('Aucun étudiant à imprimer');
        return;
    }

    $('#loadingModal').modal('show');

    $.ajax({
        url: '<?php echo e(route("payments.adra_team3.print_batch")); ?>',
        method: 'POST',
        data: {
            payment_id: paymentId,
            students_data: studentsData,
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            $('#loadingModal').modal('hide');
            const printWindow = window.open('', '_blank');
            printWindow.document.write(response);
            printWindow.document.close();
            printWindow.print();
        },
        error: function() {
            $('#loadingModal').modal('hide');
            toastr.error('Erreur lors de l\'impression');
        }
    });
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR').format(amount) + ' Ar';
}

// Test JavaScript functions
function testJavaScriptFunctions() {
    console.log('🧪 Testing JavaScript functions...');

    const tests = [];

    // Test 1: jQuery
    if (typeof jQuery !== 'undefined') {
        tests.push('✅ jQuery loaded');
    } else {
        tests.push('❌ jQuery NOT loaded');
    }

    // Test 2: Elements
    if ($('#class_selector').length > 0) {
        tests.push('✅ #class_selector found');
    } else {
        tests.push('❌ #class_selector NOT found');
    }

    if ($('#payment_selector').length > 0) {
        tests.push('✅ #payment_selector found');
    } else {
        tests.push('❌ #payment_selector NOT found');
    }

    // Test 3: Functions
    if (typeof loadClassPayments === 'function') {
        tests.push('✅ loadClassPayments function available');
    } else {
        tests.push('❌ loadClassPayments function NOT available');
    }

    if (typeof loadStudentsWithPayment === 'function') {
        tests.push('✅ loadStudentsWithPayment function available');
    } else {
        tests.push('❌ loadStudentsWithPayment function NOT available');
    }

    // Test 4: Routes
    const testUrl = '<?php echo e(route("payments.adra_team3.get_payments")); ?>';
    if (testUrl && testUrl.length > 0) {
        tests.push('✅ Route payments.adra_team3.get_payments available');
    } else {
        tests.push('❌ Route payments.adra_team3.get_payments NOT available');
    }

    // Test 5: CSRF Token
    const csrfToken = '<?php echo e(csrf_token()); ?>';
    if (csrfToken && csrfToken.length > 0) {
        tests.push('✅ CSRF token available');
    } else {
        tests.push('❌ CSRF token NOT available');
    }

    // Display results
    const results = tests.join('\n');
    console.log('Test Results:\n' + results);

    // Show in alert
    alert('Test JavaScript Functions:\n\n' + results + '\n\nVoir la console (F12) pour plus de détails.');

    // Test actual function call
    if (typeof loadClassPayments === 'function') {
        console.log('🧪 Testing loadClassPayments function call...');
        try {
            // Set a test value
            $('#class_selector').val('1');
            loadClassPayments();
            console.log('✅ loadClassPayments called successfully');
        } catch (error) {
            console.error('❌ Error calling loadClassPayments:', error);
        }
    }
}


</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\CPadv\CPA\resources\views/pages/support_team/payments/adra_team3_filter.blade.php ENDPATH**/ ?>