<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

// Auth::routes();

// Routes d'authentification personnalisées
Route::get('login', 'Auth\LoginController@showLoginForm')->name('login');
Route::post('login', 'Auth\LoginController@login');
Route::post('logout', 'Auth\LoginController@logout')->name('logout');

// Routes de réinitialisation de mot de passe
Route::get('password/reset', 'Auth\ForgotPasswordController@showLinkRequestForm')->name('password.request');
Route::post('password/email', 'Auth\ForgotPasswordController@sendResetLinkEmail')->name('password.email');
Route::get('password/reset/{token}', 'Auth\ResetPasswordController@showResetForm')->name('password.reset');
Route::post('password/reset', 'Auth\ResetPasswordController@reset')->name('password.update');

// Route pour un second type de login (exemple)
// Route::get('admin/login', 'Auth\AdminLoginController@showLoginForm')->name('admin.login');
// Route::post('admin/login', 'Auth\AdminLoginController@login')->name('admin.login.submit');





//Route::get('/test', 'TestController@index')->name('test');
Route::get('/privacy-policy', 'HomeController@privacy_policy')->name('privacy_policy');
Route::get('/terms-of-use', 'HomeController@terms_of_use')->name('terms_of_use');



Route::group(['middleware' => 'auth'], function () {

    Route::get('/', 'HomeController@dashboard')->name('home');
    Route::get('/home', 'HomeController@dashboard')->name('home');
    Route::get('/dashboard', 'HomeController@dashboard')->name('dashboard');

    Route::group(['prefix' => 'my_account'], function() {
        Route::get('/', 'MyAccountController@edit_profile')->name('my_account');
        Route::put('/', 'MyAccountController@update_profile')->name('my_account.update');
        Route::put('/change_password', 'MyAccountController@change_pass')->name('my_account.change_pass');
    });

    /*************** Support Team *****************/
    Route::group(['namespace' => 'SupportTeam',], function(){

        /*************** Students *****************/
        Route::group(['prefix' => 'students'], function(){
            Route::get('reset_pass/{st_id}', 'StudentRecordController@reset_pass')->name('st.reset_pass');
            Route::get('graduated', 'StudentRecordController@graduated')->name('students.graduated');
            Route::put('not_graduated/{id}', 'StudentRecordController@not_graduated')->name('st.not_graduated');
            Route::get('list/{class_id}', 'StudentRecordController@listByClass')->name('students.list')->middleware('teamSAT');
            Route::get('list-all', 'StudentRecordController@listAll')->name('students.list_all')->middleware('teamSAT');

            /* Promotions */
            Route::post('promote_selector', 'PromotionController@selector')->name('students.promote_selector');
            Route::get('promotion/manage', 'PromotionController@manage')->name('students.promotion_manage');
            Route::delete('promotion/reset/{pid}', 'PromotionController@reset')->name('students.promotion_reset');
            Route::delete('promotion/reset_all', 'PromotionController@reset_all')->name('students.promotion_reset_all');
            Route::get('promotion/{fc?}/{fs?}/{tc?}/{ts?}', 'PromotionController@promotion')->name('students.promotion');
            Route::post('promote/{fc}/{fs}/{tc}/{ts}', 'PromotionController@promote')->name('students.promote');

            /* Réinscription */
            Route::get('reenrollment/{prev_class_id?}/{prev_section_id?}/{new_class_id?}/{new_section_id?}', 'ReenrollmentController@index')->name('students.reenrollment');
            Route::post('reenrollment/selector', 'ReenrollmentController@selector')->name('students.reenrollment.selector');
            Route::post('reenrollment/reenroll/{prev_class_id}/{prev_section_id}/{new_class_id}/{new_section_id}', 'ReenrollmentController@reenroll')->name('students.reenrollment.reenroll');
            Route::get('reenrollment/reenroll_all/{prev_class_id}/{prev_section_id}/{new_class_id}/{new_section_id}', 'ReenrollmentController@reenrollAll')->name('students.reenrollment.reenroll_all');
            Route::post('reenrollment/search', 'ReenrollmentController@search')->name('students.reenrollment.search');
            Route::post('reenrollment/reenroll_student/{student_id}', 'ReenrollmentController@reenrollStudent')->name('students.reenrollment.reenroll_student');
            Route::get('reenrollment/export/{prev_class_id}/{prev_section_id}', 'ReenrollmentController@exportStudents')->name('students.reenrollment.export');
            Route::get('reenrollment/import', 'ReenrollmentController@importForm')->name('students.reenrollment.import.form');
            Route::post('reenrollment/import', 'ReenrollmentController@importStudents')->name('students.reenrollment.import');
            Route::post('reenrollment/batch_reenroll', 'ReenrollmentController@batchReenroll')->name('students.reenrollment.batch_reenroll');
            Route::post('reenrollment/batch_reenroll_submit', 'ReenrollmentController@batchReenrollSubmit')->name('students.reenrollment.batch_reenroll_submit');

        });

        /*************** Users *****************/
        Route::group(['prefix' => 'users'], function(){
            Route::get('reset_pass/{id}', 'UserController@reset_pass')->name('users.reset_pass');
        });

        /*************** TimeTables *****************/
        Route::group(['prefix' => 'timetables'], function(){
            Route::get('/', 'TimeTableController@index')->name('tt.index');

            Route::group(['middleware' => 'teamSA'], function() {
                Route::post('/', 'TimeTableController@store')->name('tt.store');
                Route::put('/{tt}', 'TimeTableController@update')->name('tt.update');
                Route::delete('/{tt}', 'TimeTableController@delete')->name('tt.delete');
            });

            /*************** TimeTable Records *****************/
            Route::group(['prefix' => 'records'], function(){

                Route::group(['middleware' => 'teamSA'], function(){
                    Route::get('manage/{ttr}', 'TimeTableController@manage')->name('ttr.manage');
                    Route::post('/', 'TimeTableController@store_record')->name('ttr.store');
                    Route::get('edit/{ttr}', 'TimeTableController@edit_record')->name('ttr.edit');
                    Route::put('/{ttr}', 'TimeTableController@update_record')->name('ttr.update');
                });

                Route::get('show/{ttr}', 'TimeTableController@show_record')->name('ttr.show');
                Route::get('print/{ttr}', 'TimeTableController@print_record')->name('ttr.print');
                Route::delete('/{ttr}', 'TimeTableController@delete_record')->name('ttr.destroy');

            });

            /*************** Time Slots *****************/
            Route::group(['prefix' => 'time_slots', 'middleware' => 'teamSA'], function(){
                Route::post('/', 'TimeTableController@store_time_slot')->name('ts.store');
                Route::post('/use/{ttr}', 'TimeTableController@use_time_slot')->name('ts.use');
                Route::get('edit/{ts}', 'TimeTableController@edit_time_slot')->name('ts.edit');
                Route::delete('/{ts}', 'TimeTableController@delete_time_slot')->name('ts.destroy');
                Route::put('/{ts}', 'TimeTableController@update_time_slot')->name('ts.update');
            });

        });

        /*************** Payments *****************/
        Route::group(['prefix' => 'payments'], function(){

            Route::get('verified/{class_id?}', 'PaymentController@verified')->name('payments.verified');
            Route::get('selectpaymetns', 'PaymentController@select')->name('payments.select');
            Route::get('check_unpaid', 'PaymentController@checkUnpaid')->name('payments.check_unpaid');
            Route::get('export_unpaid', 'PaymentController@exportUnpaidExcel')->name('payments.export_unpaid');
            Route::get('filter', 'PaymentController@select')->name('payments.filter');

            // ADRA & TEAM 3 Payment Management
            Route::get('adra-team3/filter', 'PaymentController@adraTeam3Filter')->name('payments.adra_team3.filter');
            Route::get('adra-team3/get-payments', 'PaymentController@getClassPayments')->name('payments.adra_team3.get_payments');
            Route::get('adra-team3/get-students', 'PaymentController@getPaymentStudents')->name('payments.adra_team3.get_students');
            Route::post('adra-team3/update-reference', 'PaymentController@updateReference')->name('payments.adra_team3.update_reference');
            Route::post('adra-team3/print-receipt/{student_id}', 'PaymentController@printAdraTeam3Receipt')->name('payments.adra_team3.print_receipt');
            Route::post('adra-team3/print-batch', 'PaymentController@printBatchReceipts')->name('payments.adra_team3.print_batch');
            Route::get('adra-team3/export-excel', 'PaymentController@exportAdraTeam3Excel')->name('payments.adra_team3.export_excel');

            Route::get('manage/{class_id?}', 'PaymentController@manage')->name('payments.manage');
            Route::get('invoice/{id}/{year?}', 'PaymentController@invoice')->name('payments.invoice');
            Route::get('receipts/{id}', 'PaymentController@receipts')->name('payments.receipts');
            Route::get('pdf_receipts/{id}', 'PaymentController@pdf_receipts')->name('payments.pdf_receipts');
            Route::post('select_year', 'PaymentController@select_year')->name('payments.select_year');
            Route::post('select_class', 'PaymentController@select_class')->name('payments.select_class');
            Route::delete('reset_record/{id}', 'PaymentController@reset_record')->name('payments.reset_record');
            Route::post('pay_now/{id}', 'PaymentController@pay_now')->name('payments.pay_now');

            Route::get('journal', 'PaymentController@journal')->name('payments.journal');
            Route::get('journal/filter', 'PaymentController@journalFilter')->name('payments.journal.filter');
            Route::get('journal/export/excel', 'PaymentController@journalExportExcel')->name('payments.journal.export.excel');
            


        });

        /*************** Pins *****************/
        Route::group(['prefix' => 'pins'], function(){
            Route::get('create', 'PinController@create')->name('pins.create');
            Route::get('/', 'PinController@index')->name('pins.index');
            Route::post('/', 'PinController@store')->name('pins.store');
            Route::get('enter/{id}', 'PinController@enter_pin')->name('pins.enter');
            Route::post('verify/{id}', 'PinController@verify')->name('pins.verify');
            Route::delete('/', 'PinController@destroy')->name('pins.destroy');
        });

        /*************** Marks *****************/
        Route::group(['prefix' => 'marks'], function(){

           // FOR teamSA
            Route::group(['middleware' => 'teamSA'], function(){
                Route::get('batch_fix', 'MarkController@batch_fix')->name('marks.batch_fix');
                Route::put('batch_update', 'MarkController@batch_update')->name('marks.batch_update');
                Route::get('tabulation/{exam?}/{class?}/{sec_id?}', 'MarkController@tabulation')->name('marks.tabulation');
                Route::post('tabulation', 'MarkController@tabulation_select')->name('marks.tabulation_select');
                Route::get('tabulation/print/{exam}/{class}/{sec_id}', 'MarkController@print_tabulation')->name('marks.print_tabulation');
            });

            // FOR teamSAT
            Route::group(['middleware' => 'teamSAT'], function(){
                Route::get('/', 'MarkController@index')->name('marks.index');
                Route::get('manage/{exam}/{class}/{section}/{subject}', 'MarkController@manage')->name('marks.manage');
                Route::put('update/{exam}/{class}/{section}/{subject}', 'MarkController@update')->name('marks.update');
                Route::put('comment_update/{exr_id}', 'MarkController@comment_update')->name('marks.comment_update');
                Route::put('skills_update/{skill}/{exr_id}', 'MarkController@skills_update')->name('marks.skills_update');
                Route::post('selector', 'MarkController@selector')->name('marks.selector');
                Route::get('bulk/{class?}/{section?}', 'MarkController@bulk')->name('marks.bulk');
                Route::post('bulk', 'MarkController@bulk_select')->name('marks.bulk_select');
            });

            Route::get('select_year/{id}', 'MarkController@year_selector')->name('marks.year_selector');
            Route::post('select_year/{id}', 'MarkController@year_selected')->name('marks.year_select');
            Route::get('show/{id}/{year}', 'MarkController@show')->name('marks.show');
            Route::get('print/{id}/{exam_id}/{year}', 'MarkController@print_view')->name('marks.print');
            Route::get('print_multiple/{id}/{year}', 'MarkController@print_multiple')->name('marks.print_multiple');
            Route::post('save_decision', 'MarkController@save_decision')->name('marks.save_decision');

        });

        Route::resource('students', 'StudentRecordController');
        Route::resource('users', 'UserController');
        Route::resource('classes', 'MyClassController');
        Route::resource('sections', 'SectionController');
        Route::resource('subjects', 'SubjectController');
        Route::resource('grades', 'GradeController');
        Route::resource('exams', 'ExamController');
        Route::resource('dorms', 'DormController');
        Route::resource('payments', 'PaymentController');





// Vous pouvez ajouter ici d'autres routes si nécessaire pour le nouveau login













        /*************** Projets *****************/
        Route::resource('projets', 'ProjetController');


    });

    /************************ AJAX ****************************/
    Route::group(['prefix' => 'ajax'], function() {
        Route::get('get_lga/{state_id}', 'AjaxController@get_lga')->name('get_lga');
        Route::get('get_class_sections/{class_id}', 'AjaxController@get_class_sections')->name('get_class_sections');
        Route::get('get_class_subjects/{class_id}', 'AjaxController@get_class_subjects')->name('get_class_subjects');
        Route::get('get_available_years', 'AjaxController@get_available_years')->name('ajax.get_available_years');
        Route::post('update_student_field', 'AjaxController@update_student_field')->name('ajax.update_student_field');
    });

});

/************************ SUPER ADMIN ****************************/
Route::group(['namespace' => 'SuperAdmin','middleware' => 'super_admin', 'prefix' => 'super_admin'], function(){

    Route::get('/settings', 'SettingController@index')->name('settings');
    Route::put('/settings', 'SettingController@update')->name('settings.update');

});

/************************ PARENT ****************************/
Route::group(['namespace' => 'MyParent','middleware' => 'my_parent',], function(){

    Route::get('/my_children', 'MyController@children')->name('my_children');

});
