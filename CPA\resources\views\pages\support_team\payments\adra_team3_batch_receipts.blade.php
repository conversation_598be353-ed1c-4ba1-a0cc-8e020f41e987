<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reçus_Batch_{{ date('Y-m-d_H-i-s') }}</title>
    <style>
        @page {
            size: 58mm auto;
            margin: 0;
        }
        
        body {
            font-family: 'Courier New', monospace;
            margin: 0;
            padding: 0;
            width: 58mm;
            font-size: 9pt;
            font-weight: bold;
            line-height: 1.1;
            color: #000;
        }
        
        .receipt {
            width: 54mm;
            margin: 0 auto 5mm auto;
            padding: 2mm;
            page-break-after: always;
        }
        
        .receipt:last-child {
            page-break-after: avoid;
        }
        
        .header {
            text-align: center;
            border-bottom: 1px dashed #000;
            padding-bottom: 2mm;
            margin-bottom: 2mm;
        }
        
        .title {
            font-size: 11pt;
            font-weight: bold;
            margin-bottom: 1mm;
        }
        
        .separator {
            text-align: center;
            margin: 1mm 0;
        }
        
        .info-section {
            margin-bottom: 2mm;
        }
        
        .info-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5mm;
            font-size: 8pt;
        }
        
        .info-label {
            font-weight: bold;
            width: 40%;
        }
        
        .info-value {
            width: 60%;
            text-align: right;
        }
        
        .payments-section {
            border-top: 1px dashed #000;
            border-bottom: 1px dashed #000;
            padding: 2mm 0;
            margin: 2mm 0;
        }
        
        .payments-title {
            text-align: center;
            font-weight: bold;
            margin-bottom: 1mm;
            font-size: 9pt;
        }
        
        .payment-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5mm;
            font-size: 8pt;
        }
        
        .payment-name {
            width: 65%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .payment-amount {
            width: 35%;
            text-align: right;
            font-weight: bold;
        }
        
        .totals-section {
            margin: 2mm 0;
        }
        
        .total-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5mm;
            font-size: 8pt;
        }
        
        .total-line.main {
            font-size: 9pt;
            font-weight: bold;
            border-top: 1px solid #000;
            padding-top: 1mm;
        }
        
        .status-section {
            background-color: #f0f0f0;
            padding: 1mm;
            margin: 2mm 0;
            border: 1px solid #000;
        }
        
        .status-line {
            display: flex;
            justify-content: space-between;
            font-size: 8pt;
            margin-bottom: 0.5mm;
        }
        
        .amount-words {
            margin: 2mm 0;
            font-size: 7pt;
            text-align: center;
            font-style: italic;
        }
        
        .footer {
            border-top: 1px dashed #000;
            padding-top: 2mm;
            margin-top: 2mm;
        }
        
        .footer-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5mm;
            font-size: 8pt;
        }
        
        .signature-section {
            margin-top: 3mm;
            text-align: center;
            font-size: 8pt;
        }
        
        .signature-line {
            border-bottom: 1px solid #000;
            width: 30mm;
            margin: 2mm auto;
            height: 5mm;
        }
        
        @media print {
            body {
                width: 58mm;
                margin: 0;
                padding: 0;
            }
            
            .receipt {
                width: 54mm;
                margin: 0 auto 5mm auto;
                padding: 2mm;
                page-break-after: always;
            }
            
            .receipt:last-child {
                page-break-after: avoid;
            }
            
            @page {
                size: 58mm auto;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    @foreach($receipts as $receiptData)
        @php
            $student = $receiptData['student'];
            $paymentRecords = $receiptData['paymentRecords'];
            $totalAmount = $receiptData['totalAmount'];
            $amountToPay = $receiptData['amountToPay'];
            $balance = $receiptData['balance'];
            $status = $receiptData['status'];
            $paymentMethod = $receiptData['paymentMethod'];
            $referenceCode = $receiptData['referenceCode'];
        @endphp
        
        <div class="receipt">
            <!-- Header -->
            <div class="header">
                <div class="title">REÇU DE PAIEMENT</div>
                <div class="separator">-------------------</div>
            </div>
            
            <!-- Student Information -->
            <div class="info-section">
                <div class="info-line">
                    <span class="info-label">Nom :</span>
                    <span class="info-value">{{ $student->user->name }}</span>
                </div>
                <div class="info-line">
                    <span class="info-label">Classe :</span>
                    <span class="info-value">{{ $student->my_class->name }}</span>
                </div>
                <div class="info-line">
                    <span class="info-label">Année :</span>
                    <span class="info-value">2024-2025</span>
                </div>
                <div class="info-line">
                    <span class="info-label">Statut :</span>
                    <span class="info-value">{{ $status }}</span>
                </div>
                <div class="info-line">
                    <span class="info-label">Code :</span>
                    <span class="info-value">{{ $referenceCode }}</span>
                </div>
            </div>
            
            <!-- Payments Section -->
            <div class="payments-section">
                <div class="payments-title">-- Paiements --</div>
                @foreach($paymentRecords as $payment)
                    <div class="payment-item">
                        <span class="payment-name">{{ $payment->payment->title }}</span>
                        <span class="payment-amount">: {{ number_format($payment->payment->amount, 0, ',', ' ') }} Ar</span>
                    </div>
                @endforeach

                <!-- Totals in payments section -->
                <div class="payment-item total-item">
                    <span class="payment-name">Total</span>
                    <span class="payment-amount">: {{ number_format($totalAmount, 0, ',', ' ') }} Ar</span>
                </div>

                @if($status === 'ADRA')
                    <div class="payment-item">
                        <span class="payment-name">Pris en charge</span>
                        <span class="payment-amount">: {{ number_format($amountToPay, 0, ',', ' ') }} Ar</span>
                    </div>
                    <div class="payment-item">
                        <span class="payment-name">Reste à payer</span>
                        <span class="payment-amount">: {{ number_format($balance, 0, ',', ' ') }} Ar</span>
                    </div>
                @endif
            </div>

            <!-- Amount in Words -->
            <div class="amount-words">
                <div class="words-title">Montant en lettres :</div>
                <div class="words-content">{{ \App\Helpers\Qs::convertToWords($amountToPay) }} ariary</div>
            </div>
            
            <!-- Footer -->
            <div class="footer">
                <div class="footer-line">
                    <span>Méthode :</span>
                    <span>{{ $status }}{{ $status === 'ADRA' ? ' (25% cash)' : '' }}</span>
                </div>
                <div class="separator">----------------------</div>
                <div class="footer-line">
                    <span>Date :</span>
                    <span>{{ \App\Helpers\DateHelper::formatFrench(now()) }}</span>
                </div>
                <div class="footer-line">
                    <span>Signature :</span>
                    <span>___________</span>
                </div>
            </div>
        </div>
    @endforeach
    
    <script>
        // Auto-print when page loads
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
