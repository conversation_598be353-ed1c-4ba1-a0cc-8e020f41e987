@extends('layouts.master')
@section('page_title', 'Gestion des Paiements ADRA & TEAM 3')

@section('page_style')
<style>
/* Modern DataTable Styling */
.table-modern {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table-modern thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
    padding: 15px 12px;
    border: none;
    position: relative;
}

.table-modern thead th:first-child {
    border-top-left-radius: 8px;
}

.table-modern thead th:last-child {
    border-top-right-radius: 8px;
}

.table-modern tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #e9ecef;
}

.table-modern tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-modern tbody td {
    padding: 12px;
    vertical-align: middle;
    border: none;
}

.table-modern tbody tr:last-child td:first-child {
    border-bottom-left-radius: 8px;
}

.table-modern tbody tr:last-child td:last-child {
    border-bottom-right-radius: 8px;
}

/* Badge Styling */
.badge-modern {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Status Badges */
.status-adra {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.status-team3 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

/* Form Controls */
.form-control-sm {
    border-radius: 6px;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.form-control-sm:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Payment Selection */
.payment-selection {
    max-width: 200px;
}

.form-check-inline {
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

/* Action Buttons */
.btn-modern {
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .table-responsive {
        border-radius: 8px;
    }

    .payment-selection {
        max-width: 150px;
    }

    .form-check-inline {
        display: block;
        margin-right: 0;
    }
}

/* DataTable Custom Styling */
.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border-radius: 6px;
    border: 1px solid #ddd;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: 6px;
    margin: 0 2px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}
</style>
@endsection

@section('content')

<div class="card">
    <div class="card-header header-elements-inline">
        <h6 class="card-title">
            <i class="icon-credit-card mr-2"></i>
            Gestion des Paiements ADRA & TEAM 3
        </h6>
        <div class="header-elements">
            <div class="list-icons">
                <a class="list-icons-item" data-action="collapse"></a>
                <a class="list-icons-item" data-action="reload"></a>
                <a class="list-icons-item" data-action="remove"></a>
            </div>
        </div>
    </div>

    <div class="card-body">
        <!-- Filter Zone -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="class_selector">Sélectionner une classe :</label>
                    <select id="class_selector" class="form-control select2" onchange="filterByClass()">
                        @foreach($classes as $class)
                            <option value="{{ $class->id }}" {{ $class->id == $selectedClassId ? 'selected' : '' }}>
                                {{ $class->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="payment_filter">Filtrer par paiements créés :</label>
                    <select id="payment_filter" class="form-control select2" multiple>
                        @foreach($payments as $payment)
                            <option value="{{ $payment->id }}">{{ $payment->title }} ({{ number_format($payment->amount, 0, ',', ' ') }} Ar)</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mb-3">
            <div class="col-md-12">
                <button type="button" class="btn btn-success" onclick="printSelectedReceipts()">
                    <i class="icon-printer mr-2"></i>Imprimer les reçus sélectionnés
                </button>
                <button type="button" class="btn btn-info" onclick="exportToExcel()">
                    <i class="icon-file-excel mr-2"></i>Exporter vers Excel
                </button>
                <button type="button" class="btn btn-warning" onclick="selectAllStudents()">
                    <i class="icon-checkmark3 mr-2"></i>Sélectionner tout
                </button>
                <button type="button" class="btn btn-secondary" onclick="clearSelection()">
                    <i class="icon-cross2 mr-2"></i>Désélectionner tout
                </button>
            </div>
        </div>

        <!-- Modern DataTable -->
        <div class="table-responsive">
            <table id="adra_team3_table" class="table table-modern datatable-button-html5-columns">
                <thead>
                    <tr>
                        <th width="5%">
                            <input type="checkbox" id="select_all" onchange="toggleAllSelection()">
                        </th>
                        <th>Nom & Prénoms</th>
                        <th>Classe</th>
                        <th>Statut</th>
                        <th>Code Référence</th>
                        <th>Paiements sélectionnés</th>
                        <th>Montant Total (100%)</th>
                        <th>Montant à Payer (statut)</th>
                        <th>Méthode de Paiement</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($studentsData as $studentData)
                        @php
                            $student = $studentData['student'];
                            $status = $studentData['status'];
                            $statusClass = $status === 'ADRA' ? 'badge-info' : 'badge-success';
                            $statusIcon = $status === 'ADRA' ? '🏛️' : '👥';
                        @endphp
                        <tr data-student-id="{{ $student->user_id }}" data-status="{{ $status }}">
                            <td>
                                <input type="checkbox" class="student-checkbox" value="{{ $student->user_id }}">
                            </td>
                            <td>
                                <div class="font-weight-semibold">{{ $student->user->name }}</div>
                                <small class="text-muted">{{ $student->adm_no }}</small>
                            </td>
                            <td>
                                <span class="badge badge-primary">{{ $student->my_class->name }}</span>
                            </td>
                            <td>
                                <span class="badge badge-modern {{ $statusClass }}">{{ $statusIcon }} {{ $status }}</span>
                            </td>
                            <td>
                                <input type="text" class="form-control form-control-sm reference-code" 
                                       value="{{ $studentData['reference_code'] }}" 
                                       data-student-id="{{ $student->user_id }}"
                                       onchange="updateReferenceCode(this)">
                            </td>
                            <td>
                                <div class="payment-selection">
                                    @foreach($payments as $payment)
                                        <div class="form-check form-check-inline">
                                            <input type="checkbox" class="form-check-input payment-checkbox" 
                                                   data-student-id="{{ $student->user_id }}"
                                                   data-payment-id="{{ $payment->id }}"
                                                   data-amount="{{ $payment->amount }}"
                                                   value="{{ $payment->id }}"
                                                   onchange="calculateAmounts({{ $student->user_id }})">
                                            <label class="form-check-label">
                                                {{ $payment->title }}
                                            </label>
                                        </div>
                                    @endforeach
                                </div>
                            </td>
                            <td>
                                <span class="total-amount" data-student-id="{{ $student->user_id }}">0 Ar</span>
                            </td>
                            <td>
                                <span class="amount-to-pay" data-student-id="{{ $student->user_id }}">0 Ar</span>
                                @if($status === 'ADRA')
                                    <small class="text-muted d-block">Reste: <span class="balance-amount" data-student-id="{{ $student->user_id }}">0 Ar</span></small>
                                @endif
                            </td>
                            <td>
                                <select class="form-control form-control-sm payment-method" data-student-id="{{ $student->user_id }}">
                                    <option value="ADRA" {{ $status === 'ADRA' ? 'selected' : '' }}>ADRA</option>
                                    <option value="TEAM3" {{ $status === 'TEAM3' ? 'selected' : '' }}>TEAM 3</option>
                                    <option value="Cash">Espèces</option>
                                    <option value="Bank">Banque</option>
                                </select>
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-primary" onclick="printIndividualReceipt({{ $student->user_id }})">
                                    <i class="icon-printer"></i> Imprimer
                                </button>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Chargement...</span>
                </div>
                <p class="mt-2">Traitement en cours...</p>
            </div>
        </div>
    </div>
</div>

@endsection

@section('page_script')
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#adra_team3_table').DataTable({
        responsive: true,
        pageLength: 25,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json'
        },
        columnDefs: [
            { orderable: false, targets: [0, 5, 9] }
        ]
    });
    
    // Initialize Select2
    $('.select2').select2({
        placeholder: 'Sélectionner...',
        allowClear: true
    });
});

// Filter by class
function filterByClass() {
    const classId = $('#class_selector').val();
    window.location.href = `{{ route('payments.adra_team3.filter') }}?class_id=${classId}`;
}

// Calculate amounts based on selected payments and status
function calculateAmounts(studentId) {
    const checkboxes = $(`.payment-checkbox[data-student-id="${studentId}"]:checked`);
    const status = $(`tr[data-student-id="${studentId}"]`).data('status');
    
    let totalAmount = 0;
    checkboxes.each(function() {
        totalAmount += parseInt($(this).data('amount'));
    });
    
    const amountToPay = status === 'ADRA' ? (totalAmount * 0.75) : totalAmount;
    const balance = status === 'ADRA' ? (totalAmount * 0.25) : 0;
    
    // Update display
    $(`.total-amount[data-student-id="${studentId}"]`).text(formatCurrency(totalAmount));
    $(`.amount-to-pay[data-student-id="${studentId}"]`).text(formatCurrency(amountToPay));
    $(`.balance-amount[data-student-id="${studentId}"]`).text(formatCurrency(balance));
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR').format(amount) + ' Ar';
}

// Update reference code
function updateReferenceCode(input) {
    const studentId = $(input).data('student-id');
    const referenceCode = $(input).val();
    
    $.ajax({
        url: '{{ route("payments.adra_team3.update_reference") }}',
        method: 'POST',
        data: {
            student_id: studentId,
            reference_code: referenceCode,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                toastr.success('Code de référence mis à jour');
            }
        },
        error: function() {
            toastr.error('Erreur lors de la mise à jour');
        }
    });
}

// Toggle all selection
function toggleAllSelection() {
    const isChecked = $('#select_all').is(':checked');
    $('.student-checkbox').prop('checked', isChecked);
}

// Select all students
function selectAllStudents() {
    $('.student-checkbox').prop('checked', true);
    $('#select_all').prop('checked', true);
}

// Clear selection
function clearSelection() {
    $('.student-checkbox').prop('checked', false);
    $('#select_all').prop('checked', false);
}

// Print individual receipt
function printIndividualReceipt(studentId) {
    const selectedPayments = [];
    $(`.payment-checkbox[data-student-id="${studentId}"]:checked`).each(function() {
        selectedPayments.push($(this).val());
    });
    
    if (selectedPayments.length === 0) {
        toastr.warning('Veuillez sélectionner au moins un paiement');
        return;
    }
    
    const paymentMethod = $(`.payment-method[data-student-id="${studentId}"]`).val();
    const referenceCode = $(`.reference-code[data-student-id="${studentId}"]`).val();
    
    // Show loading
    $('#loadingModal').modal('show');
    
    $.ajax({
        url: `{{ route('payments.adra_team3.print_receipt', '') }}/${studentId}`,
        method: 'POST',
        data: {
            selected_payments: selectedPayments,
            payment_method: paymentMethod,
            reference_code: referenceCode,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            $('#loadingModal').modal('hide');
            // Open receipt in new window for printing
            const printWindow = window.open('', '_blank');
            printWindow.document.write(response);
            printWindow.document.close();
            printWindow.print();
        },
        error: function() {
            $('#loadingModal').modal('hide');
            toastr.error('Erreur lors de l\'impression');
        }
    });
}

// Print selected receipts
function printSelectedReceipts() {
    const selectedStudents = [];
    $('.student-checkbox:checked').each(function() {
        const studentId = $(this).val();
        const selectedPayments = [];
        $(`.payment-checkbox[data-student-id="${studentId}"]:checked`).each(function() {
            selectedPayments.push($(this).val());
        });
        
        if (selectedPayments.length > 0) {
            selectedStudents.push({
                student_id: studentId,
                selected_payments: selectedPayments,
                payment_method: $(`.payment-method[data-student-id="${studentId}"]`).val(),
                reference_code: $(`.reference-code[data-student-id="${studentId}"]`).val()
            });
        }
    });
    
    if (selectedStudents.length === 0) {
        toastr.warning('Veuillez sélectionner au moins un étudiant avec des paiements');
        return;
    }
    
    $('#loadingModal').modal('show');
    
    $.ajax({
        url: '{{ route("payments.adra_team3.print_batch") }}',
        method: 'POST',
        data: {
            students_data: selectedStudents,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            $('#loadingModal').modal('hide');
            const printWindow = window.open('', '_blank');
            printWindow.document.write(response);
            printWindow.document.close();
            printWindow.print();
        },
        error: function() {
            $('#loadingModal').modal('hide');
            toastr.error('Erreur lors de l\'impression');
        }
    });
}

// Export to Excel
function exportToExcel() {
    const classId = $('#class_selector').val();
    window.location.href = `{{ route('payments.adra_team3.export_excel') }}?class_id=${classId}`;
}
</script>
@endsection
