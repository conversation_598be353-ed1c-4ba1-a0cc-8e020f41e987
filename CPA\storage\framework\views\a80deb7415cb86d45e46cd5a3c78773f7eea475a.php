<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reçu_<?php echo e($referenceCode); ?>_<?php echo e($student->user->name); ?></title>
    <style>
        @page  {
            size: 58mm auto; /* Largeur de 58mm pour l'imprimante thermique */
            margin: 0;
        }

        body {
            font-family: 'Arial', sans-serif; /* Police plus lisible */
            margin: 0;
            padding: 0;
            width: 58mm; /* Largeur fixe pour imprimante thermique 58mm */
            font-size: 10pt; /* Taille de police réduite pour s'adapter à la largeur */
            font-weight: bold;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            line-height: 1.2;
        }

        .container {
            width: 56mm; /* Légèrement plus petit que la page pour éviter les débordements */
            margin: 0 auto;
            padding: 1mm; /* Légère augmentation du padding */
        }

        .logo {
            text-align: center;
            margin-bottom: 2mm;
        }

        .logo img {
            max-width: 20mm;
            height: auto;
        }

        .school-name {
            text-align: center;
            font-size: 8pt;
            font-weight: 900;
            margin-bottom: 1mm;
            text-transform: uppercase;
        }

        .receipt-title {
            text-align: center;
            font-size: 10pt;
            font-weight: 900;
            margin-bottom: 2mm;
            text-transform: uppercase;
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
            padding: 1mm 0;
        }
        
        .info-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5mm;
            font-size: 8pt;
        }
        
        .info-label {
            font-weight: bold;
            width: 40%;
        }
        
        .info-value {
            width: 60%;
            text-align: right;
        }
        
        .payments-section {
            border-top: 1px dashed #000;
            border-bottom: 1px dashed #000;
            padding: 2mm 0;
            margin: 2mm 0;
        }
        
        .payments-title {
            text-align: center;
            font-weight: bold;
            margin-bottom: 1mm;
            font-size: 9pt;
        }
        
        .payment-item {
            margin-bottom: 0.5mm;
            font-size: 8pt;
            line-height: 1.2;
        }

        .payment-name {
            display: inline;
            font-weight: normal;
        }

        .payment-amount {
            display: inline;
            font-weight: bold;
        }

        .total-item {
            margin-top: 1mm;
            padding-top: 1mm;
        }
        
        .totals-section {
            margin: 2mm 0;
        }
        
        .total-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5mm;
            font-size: 8pt;
        }
        
        .total-line.main {
            font-size: 9pt;
            font-weight: bold;
            border-top: 1px solid #000;
            padding-top: 1mm;
        }
        
        .status-section {
            background-color: #f0f0f0;
            padding: 1mm;
            margin: 2mm 0;
            border: 1px solid #000;
        }
        
        .status-line {
            display: flex;
            justify-content: space-between;
            font-size: 8pt;
            margin-bottom: 0.5mm;
        }
        
        .amount-words {
            margin: 2mm 0;
            font-size: 7pt;
        }

        .words-title {
            font-weight: bold;
            margin-bottom: 0.5mm;
        }

        .words-content {
            font-style: italic;
            text-transform: capitalize;
        }
        
        .footer {
            border-top: 1px dashed #000;
            padding-top: 2mm;
            margin-top: 2mm;
        }
        
        .footer-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5mm;
            font-size: 8pt;
        }
        

        
        @media  print {
            body {
                width: 58mm;
                margin: 0;
                padding: 0;
            }
            
            .container {
                width: 54mm;
                margin: 0 auto;
                padding: 2mm;
            }
            
            @page  {
                size: 58mm auto;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Logo et nom de l'école -->
        <div class="logo">
            <?php if(isset($s['logo']) && $s['logo']): ?>
                <img src="<?php echo e($s['logo']); ?>" alt="Logo">
            <?php endif; ?>
        </div>

        <div class="school-name">
            <?php echo e(strtoupper(Qs::getSetting('system_name') ?? 'ÉCOLE')); ?>

        </div>

        <!-- Titre du reçu -->
        <div class="receipt-title">
            REÇU DE PAIEMENT
        </div>

        <!-- Numéro de référence -->
        <div class="ref-number">
            Réf: <?php echo e($referenceCode); ?>

        </div>

        <!-- Informations étudiant -->
        <div class="student-info">
            <div class="student-info-title">Informations Étudiant</div>
            <div class="info-line">
                <strong>Nom:</strong> <span class="student-name"><?php echo e($student->user->name); ?></span>
            </div>
            <div class="info-line">
                <strong>Classe:</strong> <?php echo e($student->my_class->name); ?>

            </div>
            <div class="info-line">
                <strong>Année:</strong> 2024-2025
            </div>
            <div class="info-line">
                <strong>Statut:</strong>
                <span class="status-badge <?php echo e($status === 'ADRA' ? 'status-adra' : 'status-team3'); ?>">
                    <?php echo e($status); ?>

                </span>
            </div>
        </div>

        <!-- Historique des paiements -->
        <div class="payment-history">
            <div class="payment-history-title">Détail des Paiements</div>
            <?php $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="payment-item">
                    <span class="payment-name"><?php echo e($payment->title); ?></span>
                    <span class="payment-amount"><?php echo e(number_format($payment->amount, 0, ',', ' ')); ?> Ar</span>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <div class="payment-item total-item">
                <span class="payment-name">TOTAL</span>
                <span class="payment-amount"><?php echo e(number_format($totalAmount, 0, ',', ' ')); ?> Ar</span>
            </div>
        </div>

        <!-- Résumé du paiement -->
        <div class="payment-summary">
            <div class="payment-summary-title">Résumé du Paiement</div>

            <?php if($status === 'ADRA'): ?>
                <div class="summary-item">
                    <span>Montant Total:</span>
                    <span><?php echo e(number_format($totalAmount, 0, ',', ' ')); ?> Ar</span>
                </div>
                <div class="summary-item">
                    <span>ADRA (75%):</span>
                    <span><strong><?php echo e(number_format($amountToPay, 0, ',', ' ')); ?> Ar</strong></span>
                </div>
                <div class="summary-item">
                    <span>Cash (25%):</span>
                    <span><strong><?php echo e(number_format($cashAmount, 0, ',', ' ')); ?> Ar</strong></span>
                </div>
            <?php else: ?>
                <div class="summary-item">
                    <span>Montant Total:</span>
                    <span><?php echo e(number_format($totalAmount, 0, ',', ' ')); ?> Ar</span>
                </div>
                <div class="summary-item">
                    <span>TEAM 3 (100%):</span>
                    <span><strong><?php echo e(number_format($amountToPay, 0, ',', ' ')); ?> Ar</strong></span>
                </div>
            <?php endif; ?>
        </div>

        <!-- Montant en lettres -->
        <div class="amount-words">
            <div class="words-title">Montant en Lettres</div>
            <?php if($status === 'ADRA'): ?>
                <div class="words-content">
                    <strong>ADRA:</strong> <?php echo e(\App\Helpers\Qs::convertToWords($amountToPay)); ?> ariary<br>
                    <strong>Cash:</strong> <?php echo e(\App\Helpers\Qs::convertToWords($cashAmount)); ?> ariary
                </div>
            <?php else: ?>
                <div class="words-content">
                    <strong>TEAM 3:</strong> <?php echo e(\App\Helpers\Qs::convertToWords($amountToPay)); ?> ariary
                </div>
            <?php endif; ?>
        </div>

        <!-- Informations de paiement -->
        <div class="footer">
            <div class="footer-item">
                <span><strong>Méthode de Paiement:</strong></span>
                <span><?php echo e($status); ?><?php echo e($status === 'ADRA' ? ' + Cash' : ''); ?></span>
            </div>
            <div class="footer-item">
                <span><strong>Date:</strong></span>
                <span><?php echo e(date('d/m/Y H:i')); ?></span>
            </div>
            <div class="footer-item">
                <span><strong>Caissier:</strong></span>
                <span><?php echo e(Auth::user()->name ?? 'Système'); ?></span>
            </div>
        </div>

        <!-- Informations caissier -->
        <div class="cashier-info">
            Reçu généré le <?php echo e(date('d/m/Y à H:i')); ?><br>
            Par: <?php echo e(Auth::user()->name ?? 'Système'); ?>

        </div>

        <!-- Ligne de découpe -->
        <div class="cut-line">
            ✂ - - - - - - - - - - - - - - - - - - - - - - ✂
        </div>
    </div>
    
    <script>
        // Auto-print when page loads
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
<?php /**PATH G:\CPadv\CPA\resources\views/pages/support_team/payments/adra_team3_thermal_receipt.blade.php ENDPATH**/ ?>